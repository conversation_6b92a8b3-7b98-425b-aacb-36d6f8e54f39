import os
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Optional, Dict, Any
from e2b_code_interpreter import Sandbox

router = APIRouter()

# Load E2B API key from environment
E2B_API_KEY = os.getenv("E2B_API_KEY")
if not E2B_API_KEY:
    raise ValueError("E2B_API_KEY not found in environment variables")

# Model for code execution request
class CodeExecutionRequest(BaseModel):
    code: str
    language: str
    timeout: Optional[int] = 30000  # Default 30 seconds

# Model for code execution response
class CodeExecutionResponse(BaseModel):
    output: str
    error: Optional[str] = None
    exitCode: int = 0

@router.post("/sandbox/execute", response_model=CodeExecutionResponse)
async def execute_code(request: CodeExecutionRequest):
    """
    Execute code using e2b code interpreter
    """
    try:
        # Create code interpreter session
        interpreter = CodeInterpreter(api_key=E2B_API_KEY)
        
        # Set timeout
        timeout_seconds = min(request.timeout / 1000, 60)  # Max 60 seconds
        
        # Execute code based on language
        if request.language.lower() in ["python", "py"]:
            result = await interpreter.python.run(request.code, timeout=timeout_seconds)
        elif request.language.lower() in ["javascript", "js"]:
            result = await interpreter.node.run(request.code, timeout=timeout_seconds)
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported language: {request.language}")
        
        # Close the interpreter session
        await interpreter.close()
        
        # Return the result
        return CodeExecutionResponse(
            output=result.stdout,
            error=result.stderr if result.stderr else None,
            exitCode=result.exit_code or 0
        )
    except Exception as e:
        # Handle any errors
        return CodeExecutionResponse(
            output="",
            error=str(e),
            exitCode=1
        )
from typing import Optional
import os
from e2b_code_interpreter import Sandbox
from pydantic import BaseModel
from utils.logger import logger

# Load E2B API key from environment
E2B_API_KEY = os.getenv("E2B_API_KEY")
if not E2B_API_KEY:
    logger.warning("E2B_API_KEY not found in environment variables")

# Model for code execution request
class CodeExecutionRequest(BaseModel):
    code: str
    language: str
    timeout: Optional[int] = 30000  # Default 30 seconds

# Model for code execution response
class CodeExecutionResponse(BaseModel):
    output: str
    error: Optional[str] = None
    exitCode: int = 0

async def execute_code(request: CodeExecutionRequest) -> CodeExecutionResponse:
    """
    Execute code using e2b code interpreter
    """
    try:
        # Create sandbox session
        sandbox = Sandbox(api_key=E2B_API_KEY)
        
        # Set timeout
        timeout_seconds = min(request.timeout / 1000, 60)  # Max 60 seconds
        
        # Execute code based on language
        if request.language.lower() in ["python", "py"]:
            # Run the code in the sandbox
            execution = await sandbox.run_code(request.code, timeout=timeout_seconds)
            
            # Get the result
            output = execution.text if execution.text else ""
            error = execution.error if hasattr(execution, 'error') and execution.error else None
            exit_code = 0 if not error else 1
        elif request.language.lower() in ["javascript", "js"]:
            # For JavaScript, we need to use node
            execution = await sandbox.run_code(request.code, timeout=timeout_seconds)
            
            # Get the result
            output = execution.text if execution.text else ""
            error = execution.error if hasattr(execution, 'error') and execution.error else None
            exit_code = 0 if not error else 1
        else:
            return CodeExecutionResponse(
                output="",
                error=f"Unsupported language: {request.language}",
                exitCode=1
            )
        
        # Close the sandbox session
        await sandbox.close()
        
        # Return the result
        return CodeExecutionResponse(
            output=output,
            error=error,
            exitCode=exit_code
        )
    except Exception as e:
        logger.error(f"Error executing code: {str(e)}")
        # Handle any errors
        return CodeExecutionResponse(
            output="",
            error=str(e),
            exitCode=1
        )
